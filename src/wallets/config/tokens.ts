import { abi as usdtAbi } from '@/wallets/abi/usdt';
import { BRC20Token, ERC20Token, NativeToken, RuneToken, Token } from '@/wallets/config/type';
import BTCIcon from '@/components/icons/coins/BTCIcon';
import USDTIcon from '@/components/icons/coins/USDTIcon';
import USDCIcon from '@/components/icons/coins/USDCIcon';
import BRCIcon from '@/components/icons/coins/BRCIcon';
import ETHIcon from '@/components/icons/coins/ETHIcon';
import { staticAsset } from '@/lib/static';
import YBTCBIcon from '@/components/icons/coins/YBTCBIcon';
import BitlayerChainIcon from '@/components/icons/BitlayerChainIcon';

const BTC: NativeToken = {
  id: 'BTC',
  name: 'BTC',
  symbol: 'BTC',
  decimals: 8,
  isNative: true,
  type: 'native',
  icon: BTCIcon,
};

const testRune: RuneToken = {
  id: '2584333:39',
  name: 'I•NEED•TEST•RUNES',
  symbol: 'ᚱ',
  decimals: 2,
  isNative: false,
  type: 'rune',
  icon: BRCIcon,
  runeId: '2584333:39',
};

const bitlayerTestRune: ERC20Token = {
  ...testRune,
  type: 'erc20',
  originalType: 'brc20',
  contractAddress: '******************************************',
};

const btcRuneDog: RuneToken = {
  id: '840000:3',
  name: 'DOG•GO•TO•THE•MOON',
  symbol: 'DOG',
  decimals: 5,
  isNative: false,
  type: 'rune',
  icon: '/images/tokens/rune-840000-3.png',
  runeId: '840000:3',
};

const bitlayerRuneDog: ERC20Token = {
  ...btcRuneDog,
  type: 'erc20',
  originalType: 'rune',
  contractAddress: '******************************************',
};

const sats: BRC20Token = {
  id: 'sats',
  name: 'SATS',
  symbol: 'SATS',
  decimals: 18,
  isNative: false,
  type: 'brc20',
  icon: '/images/tokens/brc20-sats.png',
  tick: 'sats',
};

const ordi: BRC20Token = {
  id: 'ordi',
  name: 'ORDI',
  symbol: 'ORDI',
  decimals: 18,
  isNative: false,
  type: 'brc20',
  icon: '/images/tokens/brc20-ordi.png',
  tick: 'ordi',
};

const rats: BRC20Token = {
  id: 'rats',
  name: 'RATS',
  symbol: 'RATS',
  decimals: 18,
  isNative: false,
  type: 'brc20',
  icon: '/images/tokens/brc20-rats.png',
  tick: 'rats',
};

const pi: BRC20Token = {
  id: '𝛑',
  name: '𝛑',
  symbol: '𝛑',
  decimals: 18,
  isNative: false,
  type: 'brc20',
  icon: staticAsset('/images/tokens/brc20-pi.a87db1600d.png'),
  tick: '𝛑',
};

const pizza: BRC20Token = {
  id: 'pizza',
  name: 'PIZZA',
  symbol: 'PIZZA',
  decimals: 18,
  isNative: false,
  type: 'brc20',
  icon: staticAsset('/images/tokens/brc20-pizza.a70f682024.png'),
  tick: 'pizza',
};

const brc20Core: BRC20Token = {
  id: 'core',
  name: 'CORE',
  symbol: 'CORE',
  decimals: 18,
  isNative: false,
  type: 'brc20',
  icon: staticAsset('/images/tokens/brc20-core.5962f5c5f5.png'),
  tick: 'core',
};

const bitlayerSats: ERC20Token = {
  ...sats,
  type: 'erc20',
  originalType: 'brc20',
  contractAddress: '0x8dae8b60f16a10edfac1714394688e006ff369fa',
};

const bitlayerOrdi: ERC20Token = {
  ...ordi,
  type: 'erc20',
  originalType: 'brc20',
  contractAddress: '0xde9f57a5b8844ebf607eceffaa2505bb961701a4',
};

const bitlayerRats: ERC20Token = {
  ...rats,
  type: 'erc20',
  originalType: 'brc20',
  contractAddress: '0x0d922f10d86243ceff899f15571f51951e8b20f6',
};

const bitlayerPi: ERC20Token = {
  ...pi,
  type: 'erc20',
  originalType: 'brc20',
  contractAddress: '0x8238d34b94e509129639553dfd3958152b0f523c',
};

const bitlayerPizza: ERC20Token = {
  ...pizza,
  type: 'erc20',
  originalType: 'brc20',
  contractAddress: '******************************************',
};

const bitlayerCore: ERC20Token = {
  ...brc20Core,
  type: 'erc20',
  originalType: 'brc20',
  contractAddress: '******************************************',
};

const bitlayerTestnetSats: ERC20Token = {
  ...sats,
  type: 'erc20',
  originalType: 'brc20',
  contractAddress: '******************************************',
};

export const bitlayerBTC: NativeToken = {
  id: 'BTC',
  name: 'BTC',
  symbol: 'BTC',
  decimals: 18,
  isNative: true,
  icon: BTCIcon,
  type: 'native',
};

const weth: ERC20Token = {
  id: 'WETH',
  name: 'Wrapped Ether',
  symbol: 'WETH',
  decimals: 18,
  isNative: false,
  icon: ETHIcon,
  type: 'erc20',
  contractAddress: '******************************************',
  ccip: {
    pool: '******************************************',
  },
};

const eth: NativeToken = {
  id: 'ETH',
  name: 'ETH',
  symbol: 'ETH',
  decimals: 18,
  isNative: true,
  icon: ETHIcon,
  type: 'native',
  zkbridge: {
    poolId: 20,
  },
  wrappedTokens: {
    default: weth,
  },
};

const wstETH: ERC20Token = {
  id: 'wstETH',
  name: 'wstETH',
  symbol: 'wstETH',
  decimals: 18,
  isNative: false,
  icon: '/images/tokens/wstETH.png',
  type: 'erc20',
  contractAddress: '******************************************',
  zkbridge: {
    poolId: 3,
  },
  ccip: {
    pool: '******************************************',
  },
};

const usdt: ERC20Token = {
  id: 'USDT',
  name: 'USDT',
  symbol: 'USDT',
  decimals: 6,
  isNative: false,
  icon: USDTIcon,
  type: 'erc20',
  contractAddress: '******************************************',
};

const ethUSDT: ERC20Token<typeof usdtAbi> = {
  ...usdt,
  contractAddress: '******************************************',
  zkbridge: {
    poolId: 1,
  },

  // USDT on Ethereum doesn't allow to update allowance to a new value if it's already set to a non-zero value.
  // Mark it as `continuouslyApprove: false` to tell the system that we need to reset the allowance if it's not enough.
  continuouslyApprove: false,

  // The abi of USDT on Ethereum mainnet is different from the abi of USDT on other chains.
  // It uses an custom version of the ERC20 abi.
  //
  // For example the `approve` function of the USDT abi doesn't return any value,
  // while the `approve` function of the ERC20 abi returns a boolean.
  // If we use a common ERC20 abi, the `approve` function will throw an error.
  abi: usdtAbi,
  ccip: {
    pool: '******************************************',
  },
};

const ethUSDC: ERC20Token = {
  id: 'USDC',
  name: 'USDC',
  symbol: 'USDC',
  decimals: 6,
  isNative: false,
  icon: USDCIcon,
  type: 'erc20',
  contractAddress: '******************************************',
  zkbridge: {
    poolId: 4,
  },
  permitable: true,
  permitVersion: '2',
  permitName: 'USD Coin',
  ccip: {
    pool: '******************************************',
  },
};

const bitlayerETH: ERC20Token = {
  ...eth,
  isNative: false,
  type: 'erc20',
  originalType: 'native',
  contractAddress: '******************************************',
  ccip: {
    receiver: 'router',
    pool: '******************************************',
  },
};

export const bitlayerUSDT: ERC20Token = {
  ...usdt,
  contractAddress: '******************************************',
  zkbridge: {
    poolId: 1,
  },
  ccip: {
    pool: '******************************************',
  },
};

export const bitlayerLegacyUSDC: ERC20Token = {
  ...ethUSDC,
  name: 'USDC.e',
  symbol: 'USDC',
  contractAddress: '******************************************',
  permitVersion: '1',
};

// Circle USDC
export const bitlayerUSDC: ERC20Token = {
  ...ethUSDC,
  contractAddress: '******************************************',
  ccip: {
    pool: '******************************************',
  },
};

const bitlayerWrappedStETH: ERC20Token = {
  ...wstETH,
  contractAddress: '******************************************',
  ccip: {
    pool: '******************************************',
  },
};

const bitlayerYBTCB: ERC20Token = {
  id: 'YBTC.B',
  name: 'YBTC.B',
  symbol: 'YBTC.B',
  decimals: 8,
  isNative: false,
  icon: YBTCBIcon,
  type: 'erc20',
  contractAddress: '******************************************',
  ccip: {
    pool: '******************************************',
  },
};

const avalancheYBTCB: ERC20Token = {
  ...bitlayerYBTCB,
  contractAddress: '******************************************',
  ccip: {
    pool: '******************************************',
  },
};

const bnbTestnetUSDT: ERC20Token = {
  ...usdt,
  decimals: 18,
  contractAddress: '******************************************',
};

const bnbTestnetUSDC: ERC20Token = {
  ...ethUSDC,
  decimals: 18,
  contractAddress: '******************************************',
};

export const bitlayerTestnetLegacyUSDC: ERC20Token = {
  ...ethUSDC,
  name: 'USDC.e',
  symbol: 'USDC',
  contractAddress: '******************************************',
  permitable: true,
  permitVersion: '1',
  permitName: 'USDC.e',
};

// Circle USDC
export const bitlayerTestnetUSDC: ERC20Token = {
  ...ethUSDC,
  contractAddress: '******************************************',
};

const bitlayerTestnetUSDT: ERC20Token = {
  ...usdt,
  decimals: 18,
  contractAddress: '******************************************',
};

const bnbUSDC: ERC20Token = {
  ...ethUSDC,
  contractAddress: '******************************************',
};

const bitlayerTestETH: ERC20Token = {
  ...eth,
  isNative: false,
  type: 'erc20',
  contractAddress: '******************************************',
};

const sepoliaETH: NativeToken = {
  ...eth,
};
const sepoliaUSDT: ERC20Token = {
  ...usdt,
  contractAddress: '******************************************',
};

const arbitrumUSDT: ERC20Token = {
  ...usdt,
  contractAddress: '******************************************',
};

const arbitrumUSDC: ERC20Token = {
  ...ethUSDC,
  contractAddress: '******************************************',
};

export const pellToken: ERC20Token = {
  id: 'pell',
  name: 'PELL',
  symbol: 'PELL',
  decimals: 18,
  isNative: false,
  icon: '/images/tokens/pell.png',
  type: 'erc20',
  contractAddress: '0x',
};

export const bscPell: ERC20Token = {
  ...pellToken,
};

const zkBridgeUSDT: ERC20Token = {
  id: 'zkUSDT',
  name: 'zkUSDT',
  symbol: 'zkUSDT',
  decimals: 18,
  isNative: false,
  icon: USDTIcon,
  type: 'erc20',
  contractAddress: '******************************************',
  zkbridge: {
    poolId: 1,
  },

  // For testing purpose, we don't need to continuously approve the allowance.
  continuouslyApprove: false,
};

const ethBTR: ERC20Token = {
  id: 'BTR',
  name: 'BTR',
  symbol: 'BTR',
  decimals: 18,
  isNative: false,
  icon: BitlayerChainIcon,
  type: 'erc20',
  contractAddress: '******************************************',
  ccip: {
    pool: '******************************************',
  },
};

const bitlayerBTR: ERC20Token = {
  id: 'BTR',
  name: 'BTR',
  symbol: 'BTR',
  decimals: 18,
  isNative: false,
  icon: BitlayerChainIcon,
  type: 'erc20',
  contractAddress: '******************************************',
  ccip: {
    pool: '******************************************',
  },
};

const bscBTR: ERC20Token = {
  id: 'BTR',
  name: 'BTR',
  symbol: 'BTR',
  decimals: 18,
  isNative: false,
  icon: BitlayerChainIcon,
  type: 'erc20',
  contractAddress: '******************************************',
  ccip: {
    pool: '******************************************',
  },
};

const sepoliaBTR: ERC20Token = {
  id: 'BTR',
  name: 'BTR',
  symbol: 'BTR',
  decimals: 18,
  isNative: false,
  icon: BRCIcon,
  type: 'erc20',
  contractAddress: '0x3C76ED5F760519e79C677965b150710f8b81d738',
  ccip: {
    pool: '******************************************',
  },
};

const bitlayerTestnetBTR: ERC20Token = {
  ...sepoliaBTR,
  contractAddress: '******************************************',
  ccip: undefined,
};

const tst: ERC20Token = {
  id: 'TST',
  name: 'TST',
  symbol: 'TST',
  decimals: 18,
  isNative: false,
  icon: BRCIcon,
  type: 'erc20',
  contractAddress: '******************************************',
  zkbridge: {
    poolId: 2,
  },
};

export const chainTokens: Record<string, Token[]> = {
  btc_mainnet: [BTC, ordi, sats, rats, pi, pizza, brc20Core, btcRuneDog],
  btc_testnet: [BTC, testRune, sats],

  bitlayer_testnet: [
    bitlayerBTC,
    bitlayerTestETH,
    bitlayerTestnetBTR,
    tst,
    bitlayerTestRune,
    bitlayerTestnetSats,
    bitlayerTestnetUSDT,
    bitlayerTestnetLegacyUSDC,
  ],
  bitlayer_mainnet: [
    bitlayerBTC,
    bitlayerOrdi,
    bitlayerSats,
    bitlayerRats,
    bitlayerPi,
    bitlayerPizza,
    bitlayerCore,
    bitlayerBTR,
    bitlayerUSDT,
    bitlayerUSDC,
    bitlayerWrappedStETH,
    bitlayerETH,
    bitlayerRuneDog,
    bitlayerYBTCB,
  ],

  sepolia: [sepoliaETH, sepoliaUSDT, sepoliaBTR, tst],

  ethereum: [ethBTR, ethUSDT as Token, ethUSDC, wstETH, eth],
  bsc_testnet: [bnbTestnetUSDT, bnbTestnetUSDC],
  bsc: [bscBTR],
  tron: [bitlayerUSDT],
  arbitrum: [arbitrumUSDT, arbitrumUSDC],

  avalanche: [avalancheYBTCB],
};

export const pegTokens = [bitlayerETH, bitlayerUSDT, bitlayerUSDC];

interface ZkbridgeTokenMap {
  testnet: Record<number, Token>;
  mainnet: Record<number, Token>;
}

export const zkbridgeTokenMap: ZkbridgeTokenMap = {
  testnet: {
    1: zkBridgeUSDT,
    2: tst,
  },
  mainnet: {
    1: ethUSDT as Token,
    2: ethUSDC,
    20: eth,
  },
};
