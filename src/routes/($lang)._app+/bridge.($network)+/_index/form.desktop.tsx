import { useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { useAccount } from '@/hooks/wallet/account';
import { UseEnsureOnChain } from '@/hooks/wallet/chains';
import { BaseChainType, NetworkType } from '@/wallets/config/type';
import { ChevronDown, LoaderIcon, RotateCcwIcon } from 'lucide-react';
import { ChainIcon, WalletDrawer } from '@/components/featured/wallet';
import { Trans, useTranslation } from 'react-i18next';
import {
  EstimateTip,
  FormSection,
  SwapButton,
  ThirdPartyBridgeMenuItem,
  WalletConnector,
  chainName,
  bridgeI18nKey,
  MaintenanceTip,
} from '@/modules/bridge/components/common';
import { walletDrivers } from '@/hooks/wallet/common';
import {
  FeeText,
  TransferFormProps,
  formatTotalAmount,
  SelectTokenField,
  AmountSection,
} from '@/modules/bridge/components/form';
import { ControllerProps, FieldPath, FieldValues } from 'react-hook-form';
import { FormField } from '@/components/ui/form';
import { TextField } from '@/components/ui/form-field';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useTransferHint, useValidate, useWorkflow } from '@/modules/bridge/hooks/transfer';
import { BigNumber } from 'ethers';
import { isAddress } from 'viem';
import {
  validate as isBtcAddress,
  Network as ValidationBtcNetwork,
} from 'bitcoin-address-validation';
import useChange from '@react-hook/change';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';

export const SelectChainField = <
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  current,
  options,
  ...props
}: Omit<ControllerProps<TFieldValues, TName>, 'render'> & {
  name: 'left' | 'right';
  current?: BaseChainType;
  options: BaseChainType[];
}) => {
  const selectButtonClass =
    'md:bl-min-w-52 md:bl-w-full md:bl-h-10 md:bl-text-lg/none md:bl-px-4 bl-text-white bl-border-input';
  const menuItemClass = 'bl-text-base/5 bl-flex bl-gap-2 bl-items-center';

  return (
    <FormField
      {...props}
      render={({ field }) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              type="button"
              className={selectButtonClass}
              outlineClassName="bl-border-input"
            >
              <div className="bl-flex bl-gap-2 bl-items-center bl-justify-between bl-w-full">
                <div className="bl-flex bl-gap-2 bl-items-center">
                  <ChainIcon icon={current?.icon} className="bl-size-5" />
                  {chainName({ chain: current, as: props.name })}
                </div>
                <ChevronDown className="bl-size-5 bl-text-primary bl-duration-200 group-data-[state=open]:bl-rotate-180" />
              </div>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            sideOffset={8}
            className="md:bl-min-w-52 md:bl-w-full bl-border-primary"
          >
            {options.map((chain) => {
              return (
                <DropdownMenuItem
                  key={chain.id}
                  className={menuItemClass}
                  onClick={() => field.onChange(chain.id)}
                >
                  <ChainIcon icon={chain.icon} className="bl-size-5" />
                  {chainName({ chain, as: props.name })}
                </DropdownMenuItem>
              );
            })}
            {!current?.testnet && options.some((c) => c.networkType === NetworkType.btc) && (
              <ThirdPartyBridgeMenuItem className={menuItemClass} />
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      )}
    />
  );
};

const ReceiverSection = ({
  fromChain,
  chain,
  senderAddress,
  network,
  form,
}: {
  fromChain?: BaseChainType;
  chain?: BaseChainType;
  senderAddress?: string;
  network?: NetworkType;
  form: TransferFormProps['form'];
}) => {
  const { t } = useTranslation('', { keyPrefix: bridgeI18nKey });
  const { t: commontT } = useTranslation();

  const formAddress = form.watch('address');

  const { address, driver: receiver } = useAccount({ network });
  const Icon = receiver ? walletDrivers[receiver]?.icon : null;

  const walletButtonClass = 'btn-xs bl-h-[26px] bl-w-full bl-text-sm';
  const bothEVM =
    fromChain?.networkType === NetworkType.evm && chain?.networkType === NetworkType.evm;

  const handleClickRefresh = () => {
    form.setValue('address', address!);
  };

  const renderWalletButton = () => {
    if (address && receiver && chain) {
      return (
        <WalletDrawer address={address} chain={chain} shouldSwitch={!bothEVM}>
          <Button variant="secondary" size="xs" type="button" className={walletButtonClass}>
            <div className="bl-flex bl-gap-2 bl-items-center">
              {Icon && <Icon className="bl-size-4" />}
              {walletDrivers[receiver]?.name}
            </div>
          </Button>
        </WalletDrawer>
      );
    }

    if (senderAddress) {
      return (
        <WalletConnector chain={chain}>
          <Button
            variant="default"
            overlayVariant="outline"
            size="xs"
            type="button"
            className={cn(walletButtonClass, 'bl-text-white')}
          >
            <span>{commontT('common.connect')}</span>
          </Button>
        </WalletConnector>
      );
    }

    return null;
  };

  return (
    <FormSection className="bl-px-5 bl-py-3 md:bl-space-y-1">
      <div className="bl-flex bl-items-center bl-justify-between bl-text-base/none">
        <div className="bl-flex bl-items-center bl-gap-2">
          <div>{t('recipientAddress')}</div>
          {address && address != formAddress && (
            <Tooltip>
              <TooltipTrigger asChild>
                <button onClick={handleClickRefresh}>
                  <RotateCcwIcon className="bl-size-4" />
                </button>
              </TooltipTrigger>
              <TooltipContent>Restore receive address</TooltipContent>
            </Tooltip>
          )}
        </div>
        <EstimateTip network={network} fromChain={fromChain} toChain={chain} />
      </div>
      <div className="bl-flex bl-gap-6 bl-justify-between">
        <TextField
          name="address"
          control={form.control}
          placeholder={t('receivepPlaceholder', { chainName: chain ? chain.name : '' })}
          className="bl-w-full"
          inputClassName="bl-min-h-2 bl-text-sm/5 placeholder:bl-font-body placeholder:bl-text-base/5 bl-px-0 bl-bg-background autofill:bl-bg-background autofill:bl-text-secondary focus-visible:bl-text-white focus-visible:bl-bg-background bl-border-0 focus-visible:bl-ring-offset-0 focus-visible:bl-ring-0 focus-visible:bl-ring-transparent bl-transition-colors bl-resize-none"
          spellCheck={false}
          autoComplete="off"
        />
        <div className="bl-min-w-32 bl-shrink-0 bl-pt-1">{renderWalletButton()}</div>
      </div>
    </FormSection>
  );
};

export const DesktopTransferForm = ({
  form,
  leftOptions,
  rightOptions,
  fromChain,
  fromChainType,
  toChain,
  balance,
  tokens,
  state,
  fee,
  max,
  min,
  onSubmit,
  onSwap,
}: TransferFormProps) => {
  const { t } = useTranslation('', { keyPrefix: bridgeI18nKey });
  const { address: senderAddress, chainId: actualFromChainId } = useAccount({
    network: fromChain?.networkType,
  });
  const { address: receiverAddress, chainId: actualToChainId } = useAccount({
    network: toChain?.networkType,
  });
  const formBalance = form.watch('amount');
  const tokenId = form.watch('token');
  const address = form.watch('address');
  const token = useMemo(() => tokens.find((item) => item.id === tokenId)!, [tokenId, tokens]);
  const { setValue } = form;

  useChange(receiverAddress, (current) => {
    if (!current || current === address) {
      return;
    }
    setValue('address', current);
  });
  useChange(toChain, () => {
    if (receiverAddress) {
      setValue('address', receiverAddress);
    }
  });

  const { ensure } = UseEnsureOnChain();

  const workflow = useWorkflow({ from: senderAddress, fromChain, toChain, token });
  const [isAmountValid, hint1] = useValidate(workflow, {
    fromChain,
    formBalance,
    balance: balance?.value !== undefined ? BigNumber.from(balance.value) : undefined,
    token,
    fee,
    min,
    max,
    state,
    address,
  });

  const transferHint = useTransferHint();

  const totalAmount = useMemo(() => {
    return formatTotalAmount(formBalance, fee, token);
  }, [formBalance, fee, token]);

  const [isAddressValid, hint2] = useMemo(() => {
    if (!address) {
      return [false, undefined];
    }

    let isValid = false;
    if (toChain?.networkType === NetworkType.evm && isAddress(address)) {
      isValid = true;
    } else if (toChain?.networkType === NetworkType.btc) {
      const network = toChain.testnet ? ValidationBtcNetwork.testnet : ValidationBtcNetwork.mainnet;
      isValid = isBtcAddress(address, network);
    }

    return [isValid, isValid ? undefined : t('invalidAddress')];
  }, [t, address, toChain]);

  const [canTransfer, hint] = [isAmountValid && isAddressValid, hint1 || hint2];

  const transferButtonClass = 'bl-w-full bl-h-10';

  const renderTransferButton = () => {
    const text = transferHint ? t(transferHint) : hint;
    return (
      <Button
        variant="secondary"
        size="lg"
        className={transferButtonClass}
        type="submit"
        disabled={!canTransfer}
      >
        <div className="bl-flex bl-items-center bl-gap-2">
          {state === 'loading' && <LoaderIcon className="bl-size-6 bl-animate-spin" />}
          {text ? text : t('transfer')}
        </div>
      </Button>
    );
  };

  const renderSwitchButton = () => {
    const toBeEnsureChain = fromChain?.chain.id === actualFromChainId ? toChain : fromChain;
    return (
      <Button
        variant="outlineError"
        size="lg"
        overlayVariant="secondary"
        className={cn(transferButtonClass, 'hover:bl-border-primary')}
        outlineClassName="group-hover/button:bl-border-primary"
        type="button"
        onClick={() => ensure({ chain: toBeEnsureChain })}
      >
        <div>
          <Trans i18nKey="common.switchChain" values={{ chainName: toBeEnsureChain?.name }} />
        </div>
      </Button>
    );
  };

  const renderActionButton = () => {
    if (!senderAddress) {
      return (
        <WalletConnector chain={fromChain}>
          <Button
            variant="default"
            overlayVariant="outline"
            size="lg"
            className={cn(transferButtonClass, 'bl-text-white')}
            type="button"
          >
            <span>
              <Trans i18nKey="common.connect" />
            </span>
          </Button>
        </WalletConnector>
      );
    }

    // If the sender is not connected to the correct chain, show the switch button
    // If the receiver and sender are on the same chain, skip
    // If the receiver is not connected to the correct chain, show the switch button
    if (fromChain && fromChain.chain.id !== actualFromChainId) {
      return renderSwitchButton();
    } else if (toChain?.networkType === fromChain?.networkType) {
      // do nothing
    } else if (toChain && receiverAddress && toChain.chain.id !== actualToChainId) {
      return renderSwitchButton();
    }

    return renderTransferButton();
  };

  const FooterButton = () => {
    return (
      <div className="bl-w-full bl-pt-4.5">
        {renderActionButton()}
        <MaintenanceTip tokenId={tokenId} />
      </div>
    );
  };

  return (
    <form className="bl-space-y-3" onSubmit={onSubmit}>
      <div className="bl-w-full bl-flex bl-justify-between bl-items-end bl-pb-4.5">
        <div className="bl-space-y-2.5">
          <div className="bl-text-base/none">{t('from')}</div>
          <SelectChainField
            current={fromChain}
            control={form.control}
            name="left"
            options={leftOptions}
          />
        </div>
        <SwapButton onSwap={onSwap} fromChain={fromChain} chain={toChain} />
        <div className="bl-space-y-2.5">
          <div className="bl-text-base/none">{t('to')}</div>
          <SelectChainField
            current={toChain}
            control={form.control}
            name="right"
            options={rightOptions}
          />
        </div>
      </div>
      <AmountSection balance={balance} chain={fromChain} form={form} token={token}>
        <SelectTokenField
          form={form}
          name="token"
          control={form.control}
          current={token}
          currentChain={fromChainType === 'host' ? toChain : fromChain}
          fromChains={leftOptions}
          toChains={rightOptions}
        />
      </AmountSection>
      <ReceiverSection
        network={toChain?.networkType}
        fromChain={fromChain}
        chain={toChain}
        senderAddress={senderAddress}
        form={form}
      />
      <FormSection className="bl-px-4 bl-py-3">
        <div className="bl-grid bl-grid-cols-2">
          <div className="bl-flex bl-gap-1">
            <div className="bl-text-sm/4">{t('fee')}</div>
            <div className="bl-min-w-20 bl-text-white bl-text-base/4">
              <FeeText fee={fee} chain={fromChain} />
            </div>
          </div>
          <div className="bl-flex bl-gap-1">
            <div className="bl-text-sm/4">{t('total')}</div>
            <div className="bl-w-20 bl-text-white bl-text-base/4 bl-whitespace-nowrap">
              {formBalance && fee && token ? (
                <span className="bl-text-primary">{totalAmount}</span>
              ) : (
                '--'
              )}
            </div>
          </div>
        </div>
      </FormSection>
      <FooterButton />
    </form>
  );
};
