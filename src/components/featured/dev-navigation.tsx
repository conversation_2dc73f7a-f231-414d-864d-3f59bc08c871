import { cn } from '@/lib/utils';
import { useNavigate, useLocation } from '@remix-run/react';
import { Link } from '@/components/i18n/link';
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from '@/components/ui/navigation-menu';
import { HeadMenu, LinkData, LinkRendererProps } from './header';
// import SpannerIcon from '@/components/icons/SpannerIcon';
// import Exchange2Icon from '@/components/icons/Exchange2Icon';
// import FaucetIcon from '@/components/icons/Faucet2Icon';
import BridgeIcon from '@/components/icons/BridgeIcon';
import FlashIcon from '@/components/icons/FlashIcon';
import FinalityIcon from '@/components/icons/FinalityIcon';
import BitcoinRollupIcon from '@/components/icons/BitcoinRollupIcon';
import OpVmIcon from '@/components/icons/OpVmIcon';
import BfStarkSIcon from '@/components/icons/BfStarkSIcon';
import BitvmContributionIcon from '@/components/icons/BitvmContributionIcon';
import { useScramble } from 'use-scramble';
import { useInterval } from 'ahooks';

import MiningGalaNavIcon2 from '@/components/icons/mining-gala/NavIcon2';

// import BagIcon from '@/components/icons/BagIcon';
// import ArticleIcon from '@/components/icons/ArticleIcon';
import EtherscanIcon from '@/components/icons/EtherscanIcon';
import { bitlayerMainnet, bitlayerTestnet } from '@/wallets/config/chains';
import { useMediaQuery } from '@react-hook/media-query';
import { Drawer, DrawerContent, DrawerPortal, DrawerTrigger } from '@/components/ui/drawer';
import MenuIcon from '@/components/icons/MenuIcon';
import { useCallback, useContext, useState } from 'react';
import { HeaderContext } from '@/hooks/header';
import { AnimatePresence } from 'framer-motion';
import { useScroll } from '@use-gesture/react';
import WorldIcon from '@/components/icons/WorldIcon';
import { useTranslation } from 'react-i18next';
import {
  // DevCommunitiesIcon,
  DevelopersIcon,
  DocumentationIcon,
  GithubIcon,
  HackathonIcon,
  MultisigWalletIcon,
  TheGraphIcon,
  FaucetIcon,
  DappSecurityManualIcon,
  SecurityNetworkIcon,
  TelegramIcon,
  BlogIcon,
  PodcastIcon,
} from '../icons/DeveloperIcon';
import { USDCOutlineIcon } from '../icons/coins/USDCIcon';

import RaceFinishIcon from '@/components/icons/RaceFinishIcon';
import BtcfiIcon from '@/components/icons/BtcfiIcon';
import GalaIcon from '@/components/icons/GalaIcon';
import HelmetIcon from '@/components/icons/HelmetIcon';
import FireVoteIcon from '@/components/icons/FireVoteIcon';
import AirdropIcon from '@/components/icons/AirdropIcon';
import MatrixCubeIcon from '../icons/MatrixCubeIcon';
import { FileTerminalIcon } from 'lucide-react';

const isDev = import.meta.env.MODE === 'development';

export const defaultChain = isDev ? bitlayerTestnet : bitlayerMainnet;

const MainNetDocsLinks = {
  Documentation: 'https://docs.bitlayer.org/docs/Build/GettingStarted/QuickStart',
  'Build Now': 'https://docs.bitlayer.org/docs/Build/GettingStarted/QuickStart/',
  'The Graph': 'https://docs.bitlayer.org/docs/Build/DeveloperResources/Indexers/TheGraph',
  'Get start': 'https://docs.bitlayer.org/docs/Build/GettingStarted/QuickStart',
  'Run a node': 'https://docs.bitlayer.org/docs/Build/GettingStarted/CompileAndRun',
  'Bitlayer Architecture': 'https://docs.bitlayer.org/docs/Learn/Bitlayer%20Rollup/system-design/',
  'Bitlayer Roadmap': 'https://docs.bitlayer.org/docs/Learn/Introduction/Roadmap/',
  'Dapp Security Manual': 'https://docs.bitlayer.org/docs/Build/TrackPack/DappSecurityManual',
  'Security Network': 'https://docs.bitlayer.org/docs/Build/TrackPack/SecurityNetwork',
  'Opensource tools': 'https://docs.bitlayer.org/docs/Build/TrackPack/OpensourceTools/',
  'Explore More Tools': 'https://docs.bitlayer.org/docs/Build/TrackPack/BoostTools/',
  'More Supports': 'https://docs.bitlayer.org/docs/Learn/TrackPack/OperationSupport',
};

const userLinks = [
  {
    name: 'navigation.links.dappCenter',
    link: '/ready-player-one/dapps-center',
    icon: MiningGalaNavIcon2,
    hint: 'navigation.links.dappCenterEcosystem',
    isBlank: true,
  },
  {
    name: 'navigation.links.btcfi',
    link: '/btcfi',
    icon: BtcfiIcon,
    hint: 'navigation.links.btcfiDesc',
    isBlank: true,
  },
  {
    name: 'navigation.links.miningGala3',
    link: '/mining-gala',
    icon: GalaIcon,
    hint: 'navigation.links.developersSection.developerSupport.miningGalaHint',
    isBlank: true,
  },
  {
    name: 'navigation.links.readyPlayerOne',
    link: '/airdrop/ready-player-one',
    icon: RaceFinishIcon,
    hint: 'navigation.links.hint1',
    isBlank: true,
  },
  {
    name: 'navigation.links.luckyHelmet',
    link: '/airdrop/lucky-helmet',
    icon: HelmetIcon,
    hint: 'navigation.links.hint2',
    isBlank: true,
  },
  {
    name: 'navigation.links.leaderboard',
    link: '/ready-player-one/leaderboard',
    icon: FireVoteIcon,
    hint: 'navigation.links.dappCenterHint',
    isBlank: true,
  },
];

export const DocsLinks = MainNetDocsLinks;

const bitlayerV2Link: LinkData[] = [
  {
    name: 'navigation.links.bitlayerV2',
    children: [
      {
        name: 'navigation.links.bitvmBridge',
        link: 'https://bitvmbridge.bitlayer.org/',
        icon: FinalityIcon,
        type: 'product',
        hideOrder: true,
      },
      {
        name: 'navigation.links.bitcoinRollup',
        link: 'https://docs.bitlayer.org/docs/Learn/Bitlayer%20Rollup/overview',
        icon: BitcoinRollupIcon,
        type: 'product',
        hideOrder: true,
      },
      {
        name: 'navigation.links.bitvm',
        link: '/bitvm',
        icon: OpVmIcon,
        type: 'innovation',
        hideOrder: true,
      },
      {
        name: 'navigation.links.bFStarks',
        link: 'https://blog.bitlayer.org/introduce-bf-stark/',
        icon: BfStarkSIcon,
        type: 'innovation',
        hideOrder: true,
      },
      {
        name: 'navigation.links.bitVMContribution',
        link: 'https://blog.bitlayer.org/contributions-to-bitVM-from-bitlayer-research-team/',
        icon: BitvmContributionIcon,
        type: 'innovation',
        hideOrder: true,
      },
      {
        name: 'navigation.links.whitepaper',
        link: 'https://static.bitlayer.org/Bitlayer-Technical-Whitepaper.pdf',
        icon: FileTerminalIcon,
        type: 'innovation',
        hideOrder: true,
      },
      {
        name: 'navigation.links.MiCar_Whitepaper',
        link: 'https://static.bitlayer.org/Bitlayer_iXBRL_Whitepaper.html',
        icon: FileTerminalIcon,
        type: 'innovation',
        hideOrder: true,
      },
    ],
  },
];
const developerLinks: LinkData[] = [
  {
    name: 'navigation.links.developers',
    link: '/developers',
    icon: DevelopersIcon,
    hint: 'navigation.links.developersSection.hint',
  },
  {
    name: 'navigation.links.developersSection.documentation',
    link: DocsLinks['Documentation'],
    icon: DocumentationIcon,
    hint: 'navigation.links.developersSection.documentationHint',
  },
  {
    name: 'navigation.links.developersSection.tools.title',
    hint: 'navigation.links.developersSection.tools.hint',
    children: [
      {
        name: 'navigation.links.developersSection.tools.mainnetScan',
        link: 'https://www.btrscan.com/',
        icon: EtherscanIcon,
        hint: 'navigation.links.developersSection.tools.mainnetScanHint',
      },
      {
        name: 'navigation.links.developersSection.tools.testnetScan',
        link: 'https://testnet.btrscan.com/',
        icon: EtherscanIcon,
        hint: 'navigation.links.developersSection.tools.testnetScanHint',
      },
      {
        name: 'navigation.links.developersSection.tools.faucet',
        link: '/faucet',
        icon: FaucetIcon,
        hint: 'navigation.links.developersSection.tools.faucetHint',
      },
      {
        name: 'navigation.links.testnetBridge',
        link: '/bridge/testnet',
        icon: BridgeIcon,
        hint: 'navigation.links.testnetBridgeHint',
      },
      {
        name: 'navigation.links.developersSection.tools.theGraph',
        link: DocsLinks['The Graph'],
        icon: TheGraphIcon,
        hint: 'navigation.links.developersSection.tools.theGraphHint',
      },
      {
        name: 'navigation.links.developersSection.tools.multisigWallet',
        link: 'https://multisign.bitlayer.org/welcome',
        icon: MultisigWalletIcon,
        hint: 'navigation.links.developersSection.tools.multisigWalletHint',
      },
    ],
  },
  {
    name: 'navigation.links.developersSection.security.title',
    hint: 'navigation.links.developersSection.security.hint',
    children: [
      {
        name: 'navigation.links.developersSection.security.dappSecurityManual',
        link: DocsLinks['Dapp Security Manual'],
        icon: DappSecurityManualIcon,
        hint: 'navigation.links.developersSection.security.dappSecurityManualHint',
      },
      {
        name: 'navigation.links.developersSection.security.securityNetwork',
        link: DocsLinks['Security Network'],
        icon: SecurityNetworkIcon,
        hint: 'navigation.links.developersSection.security.securityNetworkHint',
      },
    ],
  },
  {
    name: 'navigation.links.developersSection.developerSupport.title',
    hint: 'navigation.links.developersSection.developerSupport.hint',
    link: '/developers#operationalSupport',
    icon: HackathonIcon,
    // children: userLinks,
  },
  // {
  //   name: 'navigation.links.developersSection.hackathon',
  //   link: '/developers',
  //   icon: HackathonIcon,
  //   hint: 'navigation.links.developersSection.hackathonHint',
  // },
  {
    name: 'navigation.links.developersSection.github',
    link: 'https://github.com/bitlayer-org',
    icon: GithubIcon,
    hint: 'navigation.links.developersSection.githubHint',
  },
  {
    name: 'navigation.links.developersSection.devCommunities',
    // link: 'https://t.me/bitlayerbuilders',
    // icon: DevCommunitiesIcon,
    hint: 'navigation.links.developersSection.devCommunitiesHint',
    children: [
      {
        name: 'navigation.links.developersSection.telegram',
        // link: t('resources.telegram'),
        link: 'https://t.me/bitlayerofficial',
        icon: TelegramIcon,
      },
      {
        name: 'navigation.footer.blog',
        link: 'https://blog.bitlayer.org/',
        icon: BlogIcon,
      },
      {
        name: 'navigation.footer.Podcast',
        link: 'https://blog.bitlayer.org/?types=audio',
        icon: PodcastIcon,
      },
    ],
  },
  {
    name: 'navigation.links.developersSection.aiGrant',
    link: '/ai-boostcamp',
    icon: MatrixCubeIcon,
    hint: 'navigation.links.developersSection.aiGrantHint',
  },
];

const bridgeLinks = {
  name: 'navigation.links.bridge',
  default: 'navigation.links.bridge',
  children: [
    {
      name: 'navigation.links.bridge',
      link: '/bridge',
      icon: BridgeIcon,
      isBlank: true,
    },
    {
      name: 'navigation.links.flash',
      link: '/flash-bridge',
      icon: FlashIcon,
      isBlank: true,
    },
    {
      name: 'navigation.links.usdcChange',
      link: '/usdc-change',
      icon: USDCOutlineIcon,
      isBlank: true,
    },
    {
      name: 'navigation.links.quickJump',
      link: '/quick-jump',
      icon: MatrixCubeIcon,
      isBlank: true,
    },
  ],
};

const languageLinks = [
  {
    name: 'English',
    render: (props: LinkRendererProps) => <I18nLink {...props} lang="en" link="" />,
  },
  {
    name: '繁體中文',
    render: (props: LinkRendererProps) => <I18nLink {...props} lang="zh-HC" link="/zh-HC" />,
  },
  {
    name: 'Tiếng Việt',
    render: (props: LinkRendererProps) => <I18nLink {...props} lang="vi" link="/vi" />,
  },
  {
    name: '简体中文',
    render: (props: LinkRendererProps) => <I18nLink {...props} lang="zh-CN" link="/zh-CN" />,
  },
  {
    name: '日本語',
    render: (props: LinkRendererProps) => <I18nLink {...props} lang="ja" link="/ja" />,
  },
  // {
  //   name: '한국어',
  //   render: (props: LinkRendererProps) => <I18nLink {...props} lang="ko" link="/ko" />,
  // },
  {
    name: 'bahasa Indonesia',
    render: (props: LinkRendererProps) => <I18nLink {...props} lang="id" link="/id" />,
  },
  {
    name: 'Русский',
    render: (props: LinkRendererProps) => <I18nLink {...props} lang="ru" link="/ru" />,
  },
];

const navigationLinks: LinkData[] = [
  {
    name: 'navigation.links.developers',
    children: developerLinks,
  },
];

const ecosystemLinks: LinkData = {
  name: 'navigation.links.ecosystem',
  children: userLinks,
  icon: AirdropIcon,
};
const LanLink = {
  name: '',
  icon: WorldIcon,
  children: languageLinks,
};

const I18nLink = ({
  name,
  link,
  lang,
  className,
}: {
  name: string;
  link: string;
  lang: string;
  className?: string;
}) => {
  const navigate = useNavigate();
  const { pathname, search } = useLocation();
  const { i18n } = useTranslation();

  const handleClick = () => {
    const baseUrl = pathname.replace(/\/(zh-CN|vi|zh-HC|ja|ko|id|ru)/g, '');
    i18n.changeLanguage(lang);

    navigate({
      pathname: `${link}${baseUrl}`,
      search,
    });
  };
  return (
    <button className={className} onClick={handleClick}>
      {name}
    </button>
  );
};

const NavigationItemContent = ({ link, className }: { link: LinkData; className?: string }) => {
  const { t } = useTranslation();
  const [value, setValue] = useState(link.default || '');

  if (!link.children) {
    return (
      <NavigationMenuLink className={navigationMenuTriggerStyle()} asChild>
        {link.render ? (
          link.render(link)
        ) : link.link ? (
          <Link
            to={link.link}
            className="bl-gap-2 hover:bl-bg-transparent data-[active]:bl-bg-transparent data-[state=open]:bl-bg-transparent"
          >
            {link.icon && <link.icon className="bl-size-5" />}
            {t(link.name)}
          </Link>
        ) : (
          <div className="bl-gap-2 bl-cursor-pointer hover:bl-bg-transparent data-[active]:bl-bg-transparent data-[state=open]:bl-bg-transparent">
            {link.icon && <link.icon className="bl-size-5" />}
            {t(link.name)}
          </div>
        )}
      </NavigationMenuLink>
    );
  }

  const handleClickTrigger = (e: React.MouseEvent) => {
    e.preventDefault();
  };

  return (
    <>
      <NavigationMenuTrigger
        className="bl-gap-2 hover:bl-bg-transparent data-[active]:bl-bg-transparent data-[state=open]:bl-bg-transparent"
        onClick={handleClickTrigger}
      >
        {link.link ? (
          <Link
            to={link.link}
            className="bl-gap-2 hover:bl-bg-transparent data-[active]:bl-bg-transparent data-[state=open]:bl-bg-transparent"
          >
            {link.icon && <link.icon className="bl-size-5" />}
            {t(link.name)}
          </Link>
        ) : (
          <>
            {link.icon && <link.icon className="bl-size-5" />}
            {t(link.name)}
          </>
        )}
      </NavigationMenuTrigger>
      <NavigationMenuContent className={className}>
        <HeadMenu
          links={link.children || []}
          showOrder={link.showOrder}
          linkComponent={NavigationMenuLink}
          value={value}
          defaultValue={link.default}
          onValueChange={setValue}
          // onMouseHoverItem={onHover}
        />
      </NavigationMenuContent>
    </>
  );
};

const NavigationItemV2Content = ({ link, className }: { link: LinkData; className?: string }) => {
  const { t } = useTranslation();
  const [value, setValue] = useState(link.default || '');

  const handleClickTrigger = (e: React.MouseEvent) => {
    e.preventDefault();
  };

  const productLink = link.children?.filter((item) => item.type === 'product');
  const innovationLink = link.children?.filter((item) => item.type === 'innovation');

  return (
    <>
      <NavigationMenuTrigger
        className="bl-gap-2 hover:bl-bg-transparent data-[active]:bl-bg-transparent data-[state=open]:bl-bg-transparent"
        onClick={handleClickTrigger}
      >
        {link.link ? (
          <Link
            to={link.link}
            className="bl-gap-2 hover:bl-bg-transparent data-[active]:bl-bg-transparent data-[state=open]:bl-bg-transparent"
          >
            {link.icon && <link.icon className="bl-size-5" />}
            {t(link.name)}
          </Link>
        ) : (
          <>
            {link.icon && <link.icon className="bl-size-5" />}
            {t(link.name)}
          </>
        )}
      </NavigationMenuTrigger>
      <NavigationMenuContent className={className}>
        <div className="bl-h-[48px] bl-text-lg bl-flex bl-items-center bl-justify-start bl-gap-5 bl-text-black">
          <span>01</span>
          <span>{t('navigation.links.product')}</span>
        </div>
        <div className="bl-pl-6">
          <HeadMenu
            links={productLink || []}
            showOrder={false}
            linkComponent={NavigationMenuLink}
            value={value}
            defaultValue={link.default}
            onValueChange={setValue}
          />
        </div>
        <div className="bl-h-[48px] bl-flex bl-items-center bl-justify-start bl-gap-5 bl-text-lg bl-text-black">
          <span>02</span>
          <span>{t('navigation.links.innovation')}</span>
        </div>
        <div className="bl-pl-6">
          <HeadMenu
            links={innovationLink || []}
            showOrder={false}
            linkComponent={NavigationMenuLink}
            value={value}
            defaultValue={link.default}
            onValueChange={setValue}
            // onMouseHoverItem={onHover}
          />
        </div>
      </NavigationMenuContent>
    </>
  );
};

const DesktopNavigation = () => {
  return (
    <div className="bl-flex">
      <NavigationMenu>
        <NavigationMenuList>
          {bitlayerV2Link.map((link, index) => {
            return (
              <NavigationMenuItem key={index} value={index.toString()}>
                <NavigationItemV2Content link={link} />
              </NavigationMenuItem>
            );
          })}
        </NavigationMenuList>
      </NavigationMenu>
      <NavigationMenu>
        <NavigationMenuList>
          {navigationLinks.map((link, index) => (
            <NavigationMenuItem key={index} value={index.toString()}>
              <NavigationItemContent
                className={cn('', { 'bl-min-h-[500px]': index === 0 })}
                link={link}
              />
            </NavigationMenuItem>
          ))}
        </NavigationMenuList>
      </NavigationMenu>
      <NavigationMenu>
        <NavigationMenuList>
          <NavigationMenuItem value="bridge">
            <NavigationItemContent link={bridgeLinks} className="bl-min-w-[360px]" />
          </NavigationMenuItem>
        </NavigationMenuList>
      </NavigationMenu>
      <NavigationMenu>
        <NavigationMenuList>
          <NavigationMenuItem value="bridge">
            <NavigationItemContent link={ecosystemLinks} className="bl-min-w-[360px]" />
          </NavigationMenuItem>
        </NavigationMenuList>
      </NavigationMenu>

      <NavigationMenu>
        <NavigationMenuList>
          <NavigationMenuItem value="lan">
            <NavigationItemContent link={LanLink} className="bl-min-w-[260px]" />
          </NavigationMenuItem>
        </NavigationMenuList>
      </NavigationMenu>
    </div>
  );
};

const MobileNavigation = ({
  value,
  setValue,
  open,
  setOpen,
}: {
  value: string;
  setValue: (arg0: string) => void;
  open: boolean;
  setOpen: (arg0: boolean) => void;
}) => {
  const mobileLanLink = {
    ...LanLink,
    name: 'navigation.links.language',
  };

  return (
    <Drawer direction="right" shouldScaleBackground={false} open={open} onOpenChange={setOpen}>
      <DrawerTrigger className="btn-mask bl-p-2 bl-text-primary bl-duration-150 hover:bl-bg-accent/50 hover:bl-text-accent-foreground bl-order-last md:bl-order-none">
        <MenuIcon className="bl-size-6" onClick={() => setValue('')} />
      </DrawerTrigger>
      <DrawerPortal>
        <DrawerContent className="bl-fixed bl-top-0 bl-bottom-0 bl-right-0 bl-h-dvh bl-w-[92vw] bl-bg-primary focus-visible:bl-outline-none">
          <div className="bl-h-full bl-flex bl-flex-col bl-gap-4 bl-p-4 bl-flex-1 bl-overflow-y-scroll">
            <HeadMenu
              links={[
                ...bitlayerV2Link,
                ...navigationLinks,
                bridgeLinks,
                ecosystemLinks,
                mobileLanLink,
              ]}
              showOrder={false}
              showSubOrder={true}
              onClick={() => setOpen(false)}
              value={value}
            />
          </div>
        </DrawerContent>
      </DrawerPortal>
    </Drawer>
  );
};

const borderVariants =
  'bl-absolute bl-border-[2px] bl-border-primary bl-absolute bl-h-[6px] bl-w-[6px]';

export default function NavigationHeader() {
  const [hidden, setHidden] = useState(false);
  const { widget, pinned } = useContext(HeaderContext);
  const isDesktop = useMediaQuery('(min-width: 768px)');
  const [value, setValue] = useState('');
  const [open, setOpen] = useState(false);
  const { t } = useTranslation();
  const { ref, replay } = useScramble({
    text: t('navigation.links.btcfi'),
    range: [33, 35, 36, 37, 38, 42, 43, 45, 47, 91, 92, 93, 94, 95, 123, 124, 125],
    speed: 0.5,
    tick: 4,
    step: 2,
    scramble: 10,
    seed: 0,
    chance: 1,
    overdrive: false,
    overflow: true,
  });

  useInterval(() => {
    replay();
  }, 2000);

  const setHiddenByPin = useCallback(
    (state: boolean) => {
      if (pinned) {
        state = false;
      }
      setHidden(state);
    },
    [pinned],
  );

  useScroll(
    (state) => {
      const [, offsetY] = state.offset;
      if (offsetY === 0) {
        setHiddenByPin(false);
        return;
      }
      if (offsetY < 0) {
        return;
      }
      const [, directionY] = state.direction;
      if (directionY === 1) {
        setHiddenByPin(true);
      } else {
        setHiddenByPin(false);
      }
    },
    { target: window },
  );

  const handleClick = () => {
    setOpen(true);
    setValue('navigation.links.bitlayerV2');
  };

  return (
    <header
      className={cn(
        'bl-h-[70px] md:bl-h-20 bl-w-screen bl-fixed bl-z-30 bl-top-0 bl-left-0 bl-bg-background/80 bl-border-b bl-border-divider bl-duration-300 bl-ease-linear bl-font-body',
        {
          'bl-translate-y-[-100%]': hidden,
        },
      )}
    >
      <div className="bl-container bl-flex bl-w-full bl-h-full bl-items-center bl-justify-between bl-gap-4 md:bl-gap-10 bl-px-4 md:bl-px-0">
        <div className="bl-flex bl-gap-3 md:bl-gap-8">
          <Link className="bl-block bl-min-w-0 bl-shrink-0" to="/">
            <img
              src="/images/bitlayer-logo.png"
              className="bl-hidden md:bl-block bl-h-[36px]"
              alt="bitlayer"
            />
            <img src="/images/bitlayer.gif" className="bl-size-8 md:bl-hidden" alt="bitlayer" />
          </Link>
          <AnimatePresence>{widget}</AnimatePresence>
        </div>

        <div className="bl-grow md:bl-hidden"></div>

        <div className="bl-flex bl-gap-2 md:bl-gap-4 bl-items-center bl-justify-end md:bl-grow md:bl-w-full bl-order-last md:bl-order-none">
          {isDesktop ? (
            <DesktopNavigation />
          ) : (
            <MobileNavigation open={open} setOpen={setOpen} value={value} setValue={setValue} />
          )}
        </div>
        <div className="bl-flex bl-gap-4">
          <Link
            to="/me/events"
            target="_blank"
            className="bl-relative bl-hidden md:bl-flex hover:bl-text-white md:bl-w-[164px] bl-w-[106px] bl-h-8 md:bl-h-[46px] bl-border bl-border-primary/20 bl-flex-center bl-text-[15px] md:bl-text-xl bl-text-primary"
          >
            {t('navigation.links.userCenter')}
            <div
              className={cn(borderVariants, 'bl-border-r-0 bl-border-b-0 bl-top-0 bl-left-0')}
            ></div>
            <div
              className={cn(borderVariants, 'bl-border-l-0 bl-border-t-0 bl-right-0 bl-bottom-0')}
            ></div>
          </Link>
          <button
            className="bl-relative md:bl-hidden hover:bl-text-white md:bl-w-[164px] bl-w-[106px] bl-h-8 md:bl-h-[46px] bl-border bl-border-primary/20 bl-flex-center bl-text-[15px] md:bl-text-xl bl-text-primary"
            onClick={handleClick}
          >
            {t('navigation.links.bitlayerV2')}
            <div
              className={cn(borderVariants, 'bl-border-r-0 bl-border-b-0 bl-top-0 bl-left-0')}
            ></div>
            <div
              className={cn(borderVariants, 'bl-border-l-0 bl-border-t-0 bl-right-0 bl-bottom-0')}
            ></div>
          </button>
          <Link
            to="/btcfi"
            target="_blank"
            className="bl-relative bl-bg-primary/30 md:bl-w-[164px] bl-w-[106px] bl-h-8 md:bl-h-[46px] bl-flex-center bl-text-[15px] md:bl-text-xl bl-text-primary"
          >
            <span ref={ref}></span>
            <div
              className={cn(borderVariants, 'bl-border-r-0 bl-border-b-0 bl-top-0 bl-left-0')}
            ></div>
            <div
              className={cn(borderVariants, 'bl-border-l-0 bl-border-t-0 bl-right-0 bl-bottom-0')}
            ></div>
          </Link>
        </div>
      </div>
    </header>
  );
}
